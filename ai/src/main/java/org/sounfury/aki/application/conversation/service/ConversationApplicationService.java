package org.sounfury.aki.application.conversation.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.application.conversation.dto.*;
import org.sounfury.aki.domain.service.conversation.ConversationOrchestrationFactory;
import org.sounfury.aki.domain.service.conversation.strategy.ConversationStrategy;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.chatsession.ChatSession;
import org.sounfury.aki.domain.chatsession.SessionDomainManager;
import org.sounfury.aki.domain.chatsession.SessionId;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.UUID;

/**
 * 对话应用服务
 * 统一处理聊天和Agent模式的对话功能，被api层直接调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationApplicationService {

    private final ConversationOrchestrationFactory orchestrationFactory;
    private final SessionDomainManager sessionDomainManager;
    
    /**
     * 开始新的对话会话
     * @param request 会话开始请求
     * @return 会话开始响应
     */
    public SessionStartResponse startSession(SessionStartRequest request) {
        try {
            log.info("开始新的对话会话，角色ID: {}, 模式: {}, 是否站长: {}", 
                    request.getCharacterId(), request.getMode(), request.getIsOwner());
            
            // 生成新的会话ID
            String sessionId = UUID.randomUUID().toString();
            
            // 解析聊天模式
            ChatMode mode = parseChatMode(request.getMode());
            
            // 创建会话
            if (Boolean.TRUE.equals(request.getIsOwner())) {
                sessionDomainManager.createOwnerSession(
                        SessionId.of(sessionId), mode, request.getCharacterId());
            } else {
                sessionDomainManager.createGuestSession(
                        SessionId.of(sessionId), mode, request.getCharacterId());
            }
            
            // TODO: 获取角色开场白逻辑需要重构
            String greeting = getCharacterGreeting(request.getCharacterId());
            
            log.info("对话会话创建成功，会话ID: {}, 角色: {}, 模式: {}", 
                    sessionId, request.getCharacterId(), mode.getName());
            
            return SessionStartResponse.success(greeting, sessionId, 
                    request.getCharacterId(), mode.getName());
            
        } catch (Exception e) {
            log.error("创建对话会话失败，角色ID: {}", request.getCharacterId(), e);
            return SessionStartResponse.failure("创建会话失败: " + e.getMessage());
        }
    }
    
    /**
     * 进行对话
     * @param request 聊天请求
     * @return 聊天响应
     */
    public ChatResponse chat(ChatRequest request) {
        try {
            log.info("处理对话消息，会话ID: {}, 用户: {}, 角色: {}",
                    request.getSessionId(), request.getUserName(), request.getCharacterId());

            // 获取会话信息以确定模式
            var sessionOpt = sessionDomainManager.getSession(SessionId.of(request.getSessionId()));
            if (sessionOpt.isEmpty()) {
                return ChatResponse.failure("会话不存在", request.getSessionId());
            }

            var session = sessionOpt.get();

            // 执行对话编排逻辑
            var result = orchestrateConversation(request, session);

            if (result.success()) {
                log.info("对话处理成功，会话ID: {}", request.getSessionId());

                return ChatResponse.success(
                        result.response(),
                        result.sessionId(),
                        result.characterId(),
                        result.mode(),
                        result.strategyName()
                );
            } else {
                log.error("对话处理失败，会话ID: {}, 错误: {}",
                        request.getSessionId(), result.errorMessage());
                return ChatResponse.failure(result.errorMessage(), request.getSessionId());
            }

        } catch (Exception e) {
            log.error("对话处理异常，会话ID: {}", request.getSessionId(), e);
            return ChatResponse.failure("服务异常: " + e.getMessage(), request.getSessionId());
        }
    }
    
    /**
     * 进行流式对话
     * @param request 聊天请求
     * @return 流式聊天响应
     */
    public Flux<String> chatStream(ChatRequest request) {
        try {
            log.info("处理流式对话消息，会话ID: {}, 用户: {}, 角色: {}",
                    request.getSessionId(), request.getUserName(), request.getCharacterId());

            // 获取会话信息以确定模式
            var sessionOpt = sessionDomainManager.getSession(SessionId.of(request.getSessionId()));
            if (sessionOpt.isEmpty()) {
                return Flux.error(new RuntimeException("会话不存在"));
            }

            var session = sessionOpt.get();

            // 执行流式对话编排逻辑
            return orchestrateConversationStream(request, session)
                    .doOnComplete(() -> {
                        log.info("流式对话完成，会话ID: {}", request.getSessionId());
                    })
                    .doOnError(error -> {
                        log.error("流式对话处理失败，会话ID: {}", request.getSessionId(), error);
                    });

        } catch (Exception e) {
            log.error("流式对话处理异常，会话ID: {}", request.getSessionId(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 清空会话记忆
     * @param sessionId 会话ID
     * @return 操作结果
     */
    public boolean clearMemory(String sessionId) {
        try {
            boolean cleared = sessionDomainManager.clearSessionMemory(SessionId.of(sessionId));
            if (cleared) {
                log.info("清空会话记忆成功，会话ID: {}", sessionId);
            } else {
                log.warn("清空会话记忆失败，会话不存在: {}", sessionId);
            }
            return cleared;
        } catch (Exception e) {
            log.error("清空会话记忆异常，会话ID: {}", sessionId, e);
            return false;
        }
    }
    
    /**
     * 获取会话信息
     * @param sessionId 会话ID
     * @return 会话信息
     */
    public SessionInfo getSessionInfo(String sessionId) {
        try {
            var sessionOpt = sessionDomainManager.getSession(SessionId.of(sessionId));
            
            if (sessionOpt.isPresent()) {
                var session = sessionOpt.get();
                return SessionInfo.builder()
                        .sessionId(sessionId)
                        .exists(true)
                        .mode(session.getMode().getName())
                        .characterId(session.getCharacterId())
                        .isOwnerSession(session.isOwnerSession())
                        .messageCount(session.getMessageCount())
                        .conversationRounds(session.getConversationRounds())
                        .toolsEnabled(session.isToolsEnabled())
                        .memoryEnabled(session.isMemoryEnabled())
                        .configuration(session.getConfigurationSummary())
                        .build();
            } else {
                return SessionInfo.builder()
                        .sessionId(sessionId)
                        .exists(false)
                        .build();
            }
        } catch (Exception e) {
            log.error("获取会话信息失败，会话ID: {}", sessionId, e);
            return SessionInfo.builder()
                    .sessionId(sessionId)
                    .exists(false)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }
    
    /**
     * 解析聊天模式
     */
    private ChatMode parseChatMode(String mode) {
        if (mode == null || mode.trim().isEmpty()) {
            return ChatMode.CONVERSATION;
        }
        
        return switch (mode.toLowerCase()) {
            case "agent" -> ChatMode.AGENT;
            case "conversation" -> ChatMode.CONVERSATION;
            default -> ChatMode.CONVERSATION;
        };
    }
    
    /**
     * 获取角色开场白
     * TODO: 需要实现角色开场白获取逻辑
     */
    private String getCharacterGreeting(String characterId) {

        // 临时实现，后续需要从角色仓储获取
        return "你好！我是你的AI助手，有什么可以帮助你的吗？";
    }

    /**
     * 执行对话编排
     * @param request 聊天请求
     * @param session 会话对象
     * @return 对话编排结果
     */
    private ConversationOrchestrationResult orchestrateConversation(ChatRequest request, ChatSession session) {
        log.info("开始执行对话编排，会话ID: {}, 模式: {}",
                session.getSessionId().getValue(), session.getMode());

        try {
            // 1. 更新会话活跃时间
            sessionDomainManager.updateLastActiveTime(session.getSessionId());

            // 2. 获取对话策略
            ConversationStrategy strategy = orchestrationFactory.getStrategy(session.getMode());

            // 3. 构建策略请求
            ConversationStrategy.ConversationRequest strategyRequest =
                    new ConversationStrategy.ConversationRequest(
                            request.getMessage(),
                            request.getUserName(),
                            Boolean.TRUE.equals(request.getIsOwner()),
                            session
                    );

            // 4. 执行策略
            ConversationStrategy.ConversationResult result = strategy.execute(strategyRequest);

            // 5. 保存对话到记忆（如果策略执行成功）
            if (result.success()) {
                sessionDomainManager.addConversationRound(
                        session.getSessionId(),
                        request.getMessage(),
                        result.response()
                );
            }

            log.info("对话编排执行完成，会话ID: {}, 成功: {}",
                    session.getSessionId().getValue(), result.success());

            return ConversationOrchestrationResult.from(result, session);

        } catch (Exception e) {
            log.error("对话编排执行失败，会话ID: {}", session.getSessionId().getValue(), e);
            return ConversationOrchestrationResult.failure(
                    e.getMessage(),
                    session.getSessionId().getValue(),
                    session.getMode().getName()
            );
        }
    }

    /**
     * 执行流式对话编排
     * @param request 聊天请求
     * @param session 会话对象
     * @return 流式对话结果
     */
    private Flux<String> orchestrateConversationStream(ChatRequest request, ChatSession session) {
        log.info("开始执行流式对话编排，会话ID: {}, 模式: {}",
                session.getSessionId().getValue(), session.getMode());

        try {
            // 1. 更新会话活跃时间
            sessionDomainManager.updateLastActiveTime(session.getSessionId());

            // 2. 获取对话策略
            ConversationStrategy strategy = orchestrationFactory.getStrategy(session.getMode());

            // 3. 构建策略请求
            ConversationStrategy.ConversationRequest strategyRequest =
                    new ConversationStrategy.ConversationRequest(
                            request.getMessage(),
                            request.getUserName(),
                            Boolean.TRUE.equals(request.getIsOwner()),
                            session
                    );

            // 4. 执行流式策略
            return strategy.executeStream(strategyRequest)
                    .doOnComplete(() -> {
                        log.info("流式对话编排执行完成，会话ID: {}", session.getSessionId().getValue());
                        // 注意：流式模式下需要在客户端收集完整响应后保存到记忆
                    })
                    .doOnError(error -> {
                        log.error("流式对话编排执行失败，会话ID: {}", session.getSessionId().getValue(), error);
                    });

        } catch (Exception e) {
            log.error("流式对话编排执行异常，会话ID: {}", session.getSessionId().getValue(), e);
            return Flux.error(e);
        }
    }

    /**
     * 会话信息DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class SessionInfo {
        private String sessionId;
        private Boolean exists;
        private String mode;
        private String characterId;
        private Boolean isOwnerSession;
        private Integer messageCount;
        private Integer conversationRounds;
        private Boolean toolsEnabled;
        private Boolean memoryEnabled;
        private String configuration;
        private String errorMessage;
    }

    /**
     * 对话编排结果
     */
    public record ConversationOrchestrationResult(
            String response,
            boolean success,
            String errorMessage,
            String strategyName,
            String sessionId,
            String mode,
            String characterId
    ) {
        public static ConversationOrchestrationResult from(
                ConversationStrategy.ConversationResult result,
                ChatSession session) {
            return new ConversationOrchestrationResult(
                    result.response(),
                    result.success(),
                    result.errorMessage(),
                    result.strategyName(),
                    result.sessionId(),
                    session.getMode().getName(),
                    session.getCharacterId()
            );
        }

        public static ConversationOrchestrationResult failure(
                String errorMessage,
                String sessionId,
                String mode) {
            return new ConversationOrchestrationResult(
                    null,
                    false,
                    errorMessage,
                    "conversation-application-service",
                    sessionId,
                    mode,
                    null
            );
        }
    }
}
