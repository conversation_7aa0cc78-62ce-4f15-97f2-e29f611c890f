package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;

/**
 * 系统全局提示词领域Advisor
 * 封装系统提示词的业务逻辑
 */
@RequiredArgsConstructor
public class SystemPromptDomainAdvisor implements DomainAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    
    @Override
    public String getName() {
        return "SystemPromptDomainAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 100; // 最高优先级
    }
    
    @Override
    public AdvisorType getType() {
        return AdvisorType.SYSTEM_PROMPT;
    }
    
    /**
     * 获取系统提示词内容
     * @return 系统提示词
     */
    public String getSystemPrompt() {
        return systemPromptManager.buildBaseSystemPrompt();
    }
}
