package org.sounfury.aki.domain.common;

/**
 * 领域层Advisor抽象接口
 * 定义Advisor的基本契约，不依赖任何具体的AI框架
 */
public interface DomainAdvisor {
    
    /**
     * 获取Advisor名称
     * @return Advisor名称
     */
    String getName();
    
    /**
     * 获取执行顺序
     * 数值越小，优先级越高，越先执行
     * @return 执行顺序
     */
    int getOrder();
    
    /**
     * 获取Advisor类型
     * @return Advisor类型
     */
    AdvisorType getType();
    
    /**
     * Advisor类型枚举
     */
    enum AdvisorType {
        SYSTEM_PROMPT("系统提示词"),
        CHARACTER_CARD("角色卡"),
        USER_ADDRESS("用户称呼"),
        BEHAVIOR_GUIDE("行为指导"),
        TASK_SPECIFIC("任务特定"),
        MEMORY("记忆管理"),
        PERMISSION("权限控制"),
        TOOL_INTEGRATION("工具集成");
        
        private final String description;
        
        AdvisorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
