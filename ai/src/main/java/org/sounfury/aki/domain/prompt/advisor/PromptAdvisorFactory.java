package org.sounfury.aki.domain.prompt.advisor;

import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.service.task.enums.TaskMode;

/**
 * 提示词Advisor领域服务接口
 * 负责创建各种提示词相关的Advisor
 */
public interface PromptAdvisorFactory {
    
    /**
     * 创建系统提示词Advisor
     * 处理基础系统提示词的注入
     * @return 系统提示词Advisor
     */
    DomainAdvisor createSystemPromptAdvisor();

    /**
     * 创建用户称呼Advisor
     * 处理用户名称的个性化称呼
     * @param userName 用户名称
     * @return 用户称呼Advisor
     */
    DomainAdvisor createUserAddressAdvisor(String userName);

    /**
     * 创建行为指导Advisor
     * 处理不同场景下的行为指导提示词
     * @param behaviorType 行为类型
     * @return 行为指导Advisor
     */
    DomainAdvisor createBehaviorGuideAdvisor(TemplateType behaviorType);

    /**
     * 创建任务特定Advisor
     * 处理特定任务模式的提示词
     * @param taskMode 任务模式
     * @return 任务特定Advisor
     */
    DomainAdvisor createTaskSpecificAdvisor(TaskMode taskMode);
}
