package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.task.enums.TaskMode;

/**
 * 任务特定领域Advisor
 * 封装任务特定提示词的业务逻辑
 */
@RequiredArgsConstructor
public class TaskSpecificDomainAdvisor implements DomainAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final TaskMode taskMode;
    
    @Override
    public String getName() {
        return "TaskSpecificDomainAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 500; // 在所有基础提示词之后
    }
    
    @Override
    public AdvisorType getType() {
        return AdvisorType.TASK_SPECIFIC;
    }
    
    /**
     * 获取任务特定提示词
     * @return 任务特定提示词
     */
    public String getTaskSpecificPrompt() {
        return systemPromptManager.buildTaskSpecificPrompt(taskMode.getCode());
    }
    
    /**
     * 获取任务模式
     * @return 任务模式
     */
    public TaskMode getTaskMode() {
        return taskMode;
    }
}
