package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.chatsession.ChatMode;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;

/**
 * 聊天客户端领域服务接口
 * 提供ChatClient实例的创建和管理
 * 即获取到ChatModel后封装为ChatClient(加上工具或者其他配置)
 */
public interface ChatClientManager {
    
    /**
     * 获取基础ChatClient（不带工具）
     * @return 基础ChatClient实例
     */
    ChatClient getBasicChatClient();
    
    /**
     * 获取带工具的ChatClient
     * @param tools 工具实例列表
     * @return 配置了工具的ChatClient实例
     */
    ChatClient getChatClientWithTools(List<Object> tools);
    
    /**
     * 根据聊天模式获取ChatClient
     * @param mode 聊天模式
     * @param tools 工具实例列表（可为空）
     * @return 适合该模式的ChatClient实例
     */
    ChatClient getChatClientForMode(ChatMode mode, List<Object> tools);
    
    /**
     * 检查当前模型是否支持工具调用
     * @return 是否支持工具调用
     */
    boolean supportsToolCalling();
    
    /**
     * 检查当前模型是否支持流式输出
     * @return 是否支持流式输出
     */
    boolean supportsStreaming();
}
