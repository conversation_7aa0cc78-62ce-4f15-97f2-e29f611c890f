package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;

/**
 * 角色卡领域Advisor
 * 封装角色卡的业务逻辑
 */
@RequiredArgsConstructor
public class CharacterCardDomainAdvisor implements DomainAdvisor {
    
    private final CharacterPromptManager characterPromptManager;
    private final String characterId;
    
    @Override
    public String getName() {
        return "CharacterCardDomainAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 200; // 在系统提示词之后，用户称呼之前
    }
    
    @Override
    public AdvisorType getType() {
        return AdvisorType.CHARACTER_CARD;
    }
    
    /**
     * 获取角色卡提示词
     * @return 角色卡提示词
     */
    public String getCharacterPrompt() {
        return characterPromptManager.buildCompleteCharacterPrompt(characterId);
    }
    
    /**
     * 获取角色ID
     * @return 角色ID
     */
    public String getCharacterId() {
        return characterId;
    }
}
