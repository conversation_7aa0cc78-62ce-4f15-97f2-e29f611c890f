package org.sounfury.aki.domain.llm;

import java.util.EnumSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模型能力注册表
 * 管理不同模型提供商和具体模型的能力映射
 */
public class ModelCapabilityRegistry {
    
    /**
     * 提供商级别的能力映射
     */
    private static final Map<ModelProvider.ProviderType, Set<ModelCapability>> PROVIDER_CAPABILITIES = 
            new ConcurrentHashMap<>();
    
    /**
     * 具体模型的能力映射（覆盖提供商默认能力）
     */
    private static final Map<String, Set<ModelCapability>> MODEL_SPECIFIC_CAPABILITIES = 
            new ConcurrentHashMap<>();
    
    static {
        initializeProviderCapabilities();
        initializeModelSpecificCapabilities();
    }
    
    /**
     * 初始化提供商级别的默认能力
     */
    private static void initializeProviderCapabilities() {
        // DeepSeek 能力
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.DEEPSEEK, EnumSet.of(
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.LONG_CONTEXT,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.FUNCTION_CALL
        ));
        
        // OpenAI 能力
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.OPENAI, EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        // Claude 能力
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.CLAUDE, EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.LONG_CONTEXT,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        // Gemini 能力
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.GEMINI, EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        // Qwen 能力
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.QWEN, EnumSet.of(
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.FUNCTION_CALL
        ));
        
        // 自定义模型默认能力（保守估计）
        PROVIDER_CAPABILITIES.put(ModelProvider.ProviderType.CUSTOM, EnumSet.of(
            ModelCapability.STREAMING,
            ModelCapability.MULTILINGUAL,
            ModelCapability.ROLE_PLAYING
        ));
    }
    
    /**
     * 初始化具体模型的能力（覆盖提供商默认值）
     */
    private static void initializeModelSpecificCapabilities() {
        // DeepSeek 具体模型
        MODEL_SPECIFIC_CAPABILITIES.put("deepseek-ai/DeepSeek-V3", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.LONG_CONTEXT,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        MODEL_SPECIFIC_CAPABILITIES.put("deepseek-ai/DeepSeek-V2.5", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING
        ));
        
        // OpenAI 具体模型
        MODEL_SPECIFIC_CAPABILITIES.put("gpt-4", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        MODEL_SPECIFIC_CAPABILITIES.put("gpt-4-vision-preview", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        MODEL_SPECIFIC_CAPABILITIES.put("gpt-3.5-turbo", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING
        ));
        
        // Claude 具体模型
        MODEL_SPECIFIC_CAPABILITIES.put("claude-3-opus", EnumSet.of(
            ModelCapability.FUNCTION_CALL,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.LONG_CONTEXT,
            ModelCapability.MATHEMATICAL_REASONING,
            ModelCapability.DOCUMENT_ANALYSIS,
            ModelCapability.ROLE_PLAYING,
            ModelCapability.CREATIVE_WRITING,
            ModelCapability.DATA_ANALYSIS
        ));
        
        // 一些较老的模型可能不支持Function Call
        MODEL_SPECIFIC_CAPABILITIES.put("text-davinci-003", EnumSet.of(
            ModelCapability.STREAMING,
            ModelCapability.CODE_GENERATION,
            ModelCapability.MULTILINGUAL,
            ModelCapability.CREATIVE_WRITING
            // 不支持 FUNCTION_CALL
        ));
    }
    
    /**
     * 获取模型的能力集合
     */
    public static Set<ModelCapability> getModelCapabilities(ModelProvider provider) {
        if (provider == null) {
            return EnumSet.noneOf(ModelCapability.class);
        }
        
        String modelName = provider.getModelName();
        
        // 首先检查具体模型的能力配置
        Set<ModelCapability> modelSpecific = MODEL_SPECIFIC_CAPABILITIES.get(modelName);
        if (modelSpecific != null) {
            return EnumSet.copyOf(modelSpecific);
        }
        
        // 如果没有具体模型配置，使用提供商默认能力
        Set<ModelCapability> providerDefault = PROVIDER_CAPABILITIES.get(provider.getType());
        if (providerDefault != null) {
            return EnumSet.copyOf(providerDefault);
        }
        
        // 如果都没有，返回空集合
        return EnumSet.noneOf(ModelCapability.class);
    }
    
    /**
     * 检查模型是否支持特定能力
     */
    public static boolean hasCapability(ModelProvider provider, ModelCapability capability) {
        Set<ModelCapability> capabilities = getModelCapabilities(provider);
        return capabilities.contains(capability);
    }
    
    /**
     * 检查模型是否支持Function Call
     */
    public static boolean supportsFunctionCall(ModelProvider provider) {
        return hasCapability(provider, ModelCapability.FUNCTION_CALL);
    }
    
    /**
     * 检查模型是否适合Agent模式
     */
    public static boolean isSuitableForAgent(ModelProvider provider) {
        return supportsFunctionCall(provider);
    }
    
    /**
     * 注册自定义模型能力
     */
    public static void registerModelCapabilities(String modelName, Set<ModelCapability> capabilities) {
        MODEL_SPECIFIC_CAPABILITIES.put(modelName, EnumSet.copyOf(capabilities));
    }
    
    /**
     * 获取所有已注册的模型
     */
    public static Set<String> getRegisteredModels() {
        return Set.copyOf(MODEL_SPECIFIC_CAPABILITIES.keySet());
    }
}
