package org.sounfury.aki.domain.service.conversation.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * 聊天对话策略实现
 * 处理普通聊天对话，启用记忆和角色卡，不启用工具
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatConversationStrategy implements ConversationStrategy {

    private final ChatClientManager chatClientManager;
    private final SystemPromptManager systemPromptManager;
    private final CharacterPromptManager characterPromptManager;
    
    @Override
    public ConversationResult execute(ConversationRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行聊天对话，会话ID: {}", request.session().getSessionId().getValue());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 2. 构建消息列表
            List<Message> messages = buildMessages(request, systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用
            ChatClient chatClient = chatClientManager.getBasicChatClient();// 聊天模式不使用工具
            ChatResponse chatResponse = chatClient.prompt(prompt).call().chatResponse();
            String response = chatResponse.getResult().getOutput().getText();

            // 5. 记忆保存将在ConversationOrchestrationService中处理
            
            log.info("聊天对话执行完成，会话ID: {}, 耗时: {}ms", 
                    request.session().getSessionId().getValue(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());
            
            return ConversationResult.success(response, getStrategyName(), 
                    request.session().getSessionId().getValue());
            
        } catch (Exception e) {
            log.error("聊天对话执行失败，会话ID: {}", 
                    request.session().getSessionId().getValue(), e);
            return ConversationResult.failure(e.getMessage(), getStrategyName(), 
                    request.session().getSessionId().getValue());
        }
    }
    
    @Override
    public Flux<String> executeStream(ConversationRequest request) {
        log.info("开始执行流式聊天对话，会话ID: {}", request.session().getSessionId().getValue());
        
        try {
            // 1. 构建系统提示词
            String systemPrompt = buildSystemPrompt(request);
            
            // 2. 构建(记忆)消息列表
            List<Message> messages = buildMessages(request, systemPrompt);
            
            // 3. 创建Prompt
            Prompt prompt = new Prompt(messages);
            
            // 4. 获取ChatClient并调用流式接口
            ChatClient chatClient = chatClientManager.getBasicChatClient();
            
            return chatClient
                    .prompt(prompt)
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式聊天对话执行完成，会话ID: {}", 
                                request.session().getSessionId().getValue());
                        // 注意：流式模式下需要在客户端收集完整响应后保存到记忆
                    })
                    .doOnError(error -> {
                        log.error("流式聊天对话执行失败，会话ID: {}", 
                                request.session().getSessionId().getValue(), error);
                    });
            
        } catch (Exception e) {
            log.error("流式聊天对话执行异常，会话ID: {}", 
                    request.session().getSessionId().getValue(), e);
            return Flux.error(e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return "chat-conversation-strategy";
    }
    
    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(ConversationRequest request) {
        StringJoiner promptBuilder = new StringJoiner("\n\n");
        
        // 1. 基础系统提示词
        String basePrompt = systemPromptManager.buildBaseSystemPrompt();
        if (!basePrompt.isEmpty()) {
            promptBuilder.add(basePrompt);
        }
        
        // 2. 角色人格提示词（直接通过角色ID获取）
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(
                request.session().getCharacterId());
        if (!characterPrompt.isEmpty()) {
            promptBuilder.add(characterPrompt);
        }
        
        // 3. 用户称呼提示词
        String userPrompt = systemPromptManager.buildUserAddressPrompt(request.userName());
        if (!userPrompt.isEmpty()) {
            promptBuilder.add(userPrompt);
        }
        
        // 4. 对话行为指导
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(
                TemplateType.CONVERSATION_BEHAVIOR);
        if (!behaviorPrompt.isEmpty()) {
            promptBuilder.add(behaviorPrompt);
        }
        
        return promptBuilder.toString();
    }
    
    /**
     * 构建消息列表
     */
    private List<Message> buildMessages(ConversationRequest request, String systemPrompt) {
        List<Message> messages = new ArrayList<>();
        
        // 1. 添加系统消息
        if (!systemPrompt.isEmpty()) {
            messages.add(new SystemMessage(systemPrompt));
        }
        
        // 2. 添加历史记忆消息
        if (request.session().needsMemory()) {
            List<Message> memoryMessages = request.session().getAllMessages();
            messages.addAll(memoryMessages);
        }
        
        // 3. 添加当前用户消息
        messages.add(new UserMessage(request.userInput()));
        
        return messages;
    }
    

}
