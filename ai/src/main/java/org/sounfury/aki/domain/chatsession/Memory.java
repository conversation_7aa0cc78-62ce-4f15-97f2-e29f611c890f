package org.sounfury.aki.domain.chatsession;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.chat.messages.Message;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 记忆值对象
 * 封装聊天会话中的消息历史记录
 */
@Getter
@EqualsAndHashCode
public class Memory {
    
    /**
     * 默认最大消息数量
     */
    public static final int DEFAULT_MAX_MESSAGES = 100;
    
    /**
     * 消息列表（不可变）
     */
    private final List<Message> messages;
    
    /**
     * 最大消息数量
     */
    private final int maxMessages;
    
    /**
     * 私有构造函数
     */
    private Memory(List<Message> messages, int maxMessages) {
        if (maxMessages <= 0) {
            throw new IllegalArgumentException("最大消息数量必须大于0");
        }
        this.maxMessages = maxMessages;
        this.messages = Collections.unmodifiableList(new ArrayList<>(messages));
    }
    
    /**
     * 创建空的记忆
     * @return 空记忆实例
     */
    public static Memory empty() {
        return new Memory(new ArrayList<>(), DEFAULT_MAX_MESSAGES);
    }
    
    /**
     * 创建指定最大消息数的空记忆
     * @param maxMessages 最大消息数量
     * @return 空记忆实例
     */
    public static Memory empty(int maxMessages) {
        return new Memory(new ArrayList<>(), maxMessages);
    }
    
    /**
     * 从现有消息列表创建记忆
     * @param messages 消息列表
     * @return 记忆实例
     */
    public static Memory from(List<Message> messages) {
        return new Memory(messages, DEFAULT_MAX_MESSAGES);
    }
    
    /**
     * 从现有消息列表创建记忆
     * @param messages 消息列表
     * @param maxMessages 最大消息数量
     * @return 记忆实例
     */
    public static Memory from(List<Message> messages, int maxMessages) {
        return new Memory(messages, maxMessages);
    }
    
    /**
     * 添加消息
     * @param message 要添加的消息
     * @return 新的记忆实例
     */
    public Memory addMessage(Message message) {
        if (message == null) {
            return this;
        }
        
        List<Message> newMessages = new ArrayList<>(this.messages);
        newMessages.add(message);
        
        // 如果超过最大数量，移除最早的消息
        while (newMessages.size() > maxMessages) {
            newMessages.remove(0);
        }
        
        return new Memory(newMessages, this.maxMessages);
    }
    
    /**
     * 批量添加消息
     * @param messages 要添加的消息列表
     * @return 新的记忆实例
     */
    public Memory addMessages(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return this;
        }
        
        List<Message> newMessages = new ArrayList<>(this.messages);
        newMessages.addAll(messages);
        
        // 如果超过最大数量，移除最早的消息
        while (newMessages.size() > maxMessages) {
            newMessages.remove(0);
        }
        
        return new Memory(newMessages, this.maxMessages);
    }
    
    /**
     * 清空所有消息
     * @return 新的空记忆实例
     */
    public Memory clearMessages() {
        return new Memory(new ArrayList<>(), this.maxMessages);
    }
    
    /**
     * 获取最近的N条消息
     * @param count 消息数量
     * @return 最近的消息列表
     */
    public List<Message> getRecentMessages(int count) {
        if (count <= 0) {
            return Collections.emptyList();
        }
        
        if (count >= messages.size()) {
            return new ArrayList<>(messages);
        }
        
        int startIndex = messages.size() - count;
        return new ArrayList<>(messages.subList(startIndex, messages.size()));
    }
    
    /**
     * 获取所有消息
     * @return 所有消息列表的副本
     */
    public List<Message> getAllMessages() {
        return new ArrayList<>(messages);
    }
    
    /**
     * 获取消息数量
     * @return 消息数量
     */
    public int getMessageCount() {
        return messages.size();
    }
    
    /**
     * 检查是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return messages.isEmpty();
    }
    
    /**
     * 检查是否已满
     * @return 是否已达到最大容量
     */
    public boolean isFull() {
        return messages.size() >= maxMessages;
    }
    
    /**
     * 获取剩余容量
     * @return 剩余可添加的消息数量
     */
    public int getRemainingCapacity() {
        return Math.max(0, maxMessages - messages.size());
    }
    
    /**
     * 获取记忆摘要信息
     * @return 记忆摘要
     */
    public String getSummary() {
        return String.format("消息数量: %d/%d, 是否为空: %s, 是否已满: %s", 
                messages.size(), maxMessages, isEmpty(), isFull());
    }
}
