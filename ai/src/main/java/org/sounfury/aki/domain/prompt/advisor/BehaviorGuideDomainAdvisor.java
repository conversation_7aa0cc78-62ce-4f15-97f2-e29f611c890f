package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;

/**
 * 行为指导领域Advisor
 * 封装行为指导的业务逻辑
 */
@RequiredArgsConstructor
public class BehaviorGuideDomainAdvisor implements DomainAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final TemplateType behaviorType;
    
    @Override
    public String getName() {
        return "BehaviorGuideDomainAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 400; // 在其他基础提示词之后
    }
    
    @Override
    public AdvisorType getType() {
        return AdvisorType.BEHAVIOR_GUIDE;
    }
    
    /**
     * 获取行为指导提示词
     * @return 行为指导提示词
     */
    public String getBehaviorGuidePrompt() {
        return systemPromptManager.buildBehaviorGuidePrompt(behaviorType);
    }
    
    /**
     * 获取行为类型
     * @return 行为类型
     */
    public TemplateType getBehaviorType() {
        return behaviorType;
    }
}
