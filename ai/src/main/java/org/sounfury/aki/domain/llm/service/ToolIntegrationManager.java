package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.chatsession.ChatMode;

import java.util.List;

/**
 * 工具集成领域服务接口
 * 处理AI工具的集成和管理
 */
public interface ToolIntegrationManager {
    
    /**
     * 获取所有可用的工具实例
     * @return 工具实例列表
     */
    List<Object> getAvailableTools();
    
    /**
     * 检查指定聊天模式是否支持工具调用
     * @param mode 聊天模式
     * @return 是否支持工具调用
     */
    boolean supportsTools(ChatMode mode);
    
    /**
     * 根据聊天模式获取适用的工具
     * @param mode 聊天模式
     * @return 适用的工具实例列表
     */
    List<Object> getToolsForMode(ChatMode mode);
    
    /**
     * 检查工具是否可用
     * @return 工具是否可用
     */
    boolean areToolsAvailable();
    
    /**
     * 获取工具数量
     * @return 可用工具数量
     */
    int getToolCount();
    
    /**
     * 获取工具名称列表
     * @return 工具名称列表
     */
    List<String> getToolNames();
}
