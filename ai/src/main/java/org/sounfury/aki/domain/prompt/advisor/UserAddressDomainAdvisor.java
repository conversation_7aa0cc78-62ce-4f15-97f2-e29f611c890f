package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;

/**
 * 用户称呼领域Advisor
 * 封装用户称呼的业务逻辑
 */
@RequiredArgsConstructor
public class UserAddressDomainAdvisor implements DomainAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final String userName;
    
    @Override
    public String getName() {
        return "UserAddressDomainAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 300; // 在系统提示词和角色卡之后
    }
    
    @Override
    public AdvisorType getType() {
        return AdvisorType.USER_ADDRESS;
    }
    
    /**
     * 获取用户称呼提示词
     * @return 用户称呼提示词
     */
    public String getUserAddressPrompt() {
        return systemPromptManager.buildUserAddressPrompt(userName);
    }
    
    /**
     * 获取用户名
     * @return 用户名
     */
    public String getUserName() {
        return userName;
    }
}
