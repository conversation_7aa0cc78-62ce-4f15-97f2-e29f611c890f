package org.sounfury.aki.domain.chatsession;

import lombok.Builder;
import lombok.Getter;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天会话聚合根
 * 封装会话的状态、配置信息和记忆
 */
@Getter
public class ChatSession {

    /**
     * 会话ID
     */
    private final SessionId sessionId;

    /**
     * 聊天模式
     */
    private final ChatMode mode;

    /**
     * 角色ID
     */
    private final String characterId;

    /**
     * 是否是站长会话
     */
    private final boolean isOwnerSession;

    /**
     * 记忆（聚合成员）
     */
    private final Memory memory;

    /**
     * 对话轮次
     */
    private final int conversationRounds;

    /**
     * 是否启用记忆
     */
    private final boolean memoryEnabled;

    /**
     * 是否启用工具调用
     */
    private final boolean toolsEnabled;

    /**
     * 是否启用世界书
     */
    private final boolean worldBookEnabled;

    /**
     * 会话创建时间
     */
    private final LocalDateTime createdAt;

    /**
     * 最后活跃时间
     */
    private final LocalDateTime lastActiveAt;

    /**
     * 会话元数据
     */
    private final Map<String, Object> metadata;

    /**
     * 私有构造函数
     */
    private ChatSession(SessionId sessionId, ChatMode mode, String characterId,
                       boolean isOwnerSession, Memory memory, int conversationRounds,
                       boolean memoryEnabled, boolean toolsEnabled, boolean worldBookEnabled,
                       LocalDateTime createdAt, LocalDateTime lastActiveAt,
                       Map<String, Object> metadata) {
        this.sessionId = sessionId;
        this.mode = mode;
        this.characterId = characterId;
        this.isOwnerSession = isOwnerSession;
        this.memory = memory != null ? memory : Memory.empty();
        this.conversationRounds = conversationRounds;
        this.memoryEnabled = memoryEnabled;
        this.toolsEnabled = toolsEnabled;
        this.worldBookEnabled = worldBookEnabled;
        this.createdAt = createdAt != null ? createdAt : LocalDateTime.now();
        this.lastActiveAt = lastActiveAt != null ? lastActiveAt : LocalDateTime.now();
        this.metadata = metadata;
    }
    
    /**
     * 根据聊天模式创建默认的会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @param isOwnerSession 是否是站长会话
     * @return 聊天会话
     */
    public static ChatSession createForMode(SessionId sessionId, ChatMode mode, String characterId, boolean isOwnerSession) {
        // 根据模式设置默认配置
        switch (mode) {
            case CONVERSATION:
                return new ChatSession(
                        sessionId, mode, characterId, isOwnerSession,
                        Memory.empty(), 0,
                        true, false, false,
                        LocalDateTime.now(), LocalDateTime.now(), null
                );

            case AGENT:
                return new ChatSession(
                        sessionId, mode, characterId, isOwnerSession,
                        Memory.empty(), 0,
                        true, true, true,
                        LocalDateTime.now(), LocalDateTime.now(), null
                );

            default:
                return new ChatSession(
                        sessionId, mode, characterId, isOwnerSession,
                        Memory.empty(), 0,
                        true, false, false,
                        LocalDateTime.now(), LocalDateTime.now(), null
                );
        }
    }

    /**
     * 创建站长会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 站长聊天会话
     */
    public static ChatSession createOwnerSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createForMode(sessionId, mode, characterId, true);
    }

    /**
     * 创建游客会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 游客聊天会话
     */
    public static ChatSession createGuestSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createForMode(sessionId, mode, characterId, false);
    }
    
    // ========== 聚合根核心方法 ==========

    /**
     * 添加用户消息
     * @param content 消息内容
     * @return 新的会话实例
     */
    public ChatSession addUserMessage(String content) {
        if (!memoryEnabled || content == null || content.trim().isEmpty()) {
            return this;
        }

        Memory newMemory = this.memory.addMessage(new UserMessage(content));
        return new ChatSession(
                this.sessionId, this.mode, this.characterId, this.isOwnerSession,
                newMemory, this.conversationRounds,
                this.memoryEnabled, this.toolsEnabled, this.worldBookEnabled,
                this.createdAt, LocalDateTime.now(), this.metadata
        );
    }

    /**
     * 添加助手消息
     * @param content 消息内容
     * @return 新的会话实例
     */
    public ChatSession addAssistantMessage(String content) {
        if (!memoryEnabled || content == null || content.trim().isEmpty()) {
            return this;
        }

        Memory newMemory = this.memory.addMessage(new AssistantMessage(content));
        int newRounds = this.conversationRounds + 1; // 助手回复表示完成一轮对话

        return new ChatSession(
                this.sessionId, this.mode, this.characterId, this.isOwnerSession,
                newMemory, newRounds,
                this.memoryEnabled, this.toolsEnabled, this.worldBookEnabled,
                this.createdAt, LocalDateTime.now(), this.metadata
        );
    }

    /**
     * 添加对话轮次（用户消息+助手回复）
     * @param userContent 用户消息内容
     * @param assistantContent 助手回复内容
     * @return 新的会话实例
     */
    public ChatSession addConversationRound(String userContent, String assistantContent) {
        return this.addUserMessage(userContent).addAssistantMessage(assistantContent);
    }

    /**
     * 获取最近的消息
     * @param count 消息数量
     * @return 最近的消息列表
     */
    public List<Message> getRecentMessages(int count) {
        return memory.getRecentMessages(count);
    }

    /**
     * 获取所有消息
     * @return 所有消息列表
     */
    public List<Message> getAllMessages() {
        return memory.getAllMessages();
    }

    /**
     * 清空记忆
     * @return 新的会话实例
     */
    public ChatSession clearMemory() {
        Memory newMemory = Memory.empty(this.memory.getMaxMessages());
        return new ChatSession(
                this.sessionId, this.mode, this.characterId, this.isOwnerSession,
                newMemory, 0, // 重置对话轮次
                this.memoryEnabled, this.toolsEnabled, this.worldBookEnabled,
                this.createdAt, LocalDateTime.now(), this.metadata
        );
    }

    /**
     * 更新最后活跃时间
     * @return 新的会话实例
     */
    public ChatSession updateLastActiveTime() {
        return new ChatSession(
                this.sessionId, this.mode, this.characterId, this.isOwnerSession,
                this.memory, this.conversationRounds,
                this.memoryEnabled, this.toolsEnabled, this.worldBookEnabled,
                this.createdAt, LocalDateTime.now(), this.metadata
        );
    }
    
    /**
     * 检查会话是否过期
     * @param timeoutMinutes 超时分钟数
     * @return 是否过期
     */
    public boolean isExpired(int timeoutMinutes) {
        return lastActiveAt.isBefore(LocalDateTime.now().minusMinutes(timeoutMinutes));
    }
    
    // ========== 业务规则方法 ==========

    /**
     * 检查是否支持工具调用
     * 只有站长的Agent模式会话才能使用工具
     * @return 是否支持工具调用
     */
    public boolean supportsToolCalling() {
        return toolsEnabled && (mode == ChatMode.AGENT) && isOwnerSession;
    }

    /**
     * 检查是否可以使用Agent工具
     * 基于用户身份和模式判断
     * @return 是否可以使用Agent工具
     */
    public boolean canUseAgentTools() {
        return isOwnerSession && (mode == ChatMode.AGENT) && toolsEnabled;
    }

    /**
     * 检查是否需要记忆
     * @return 是否需要记忆
     */
    public boolean needsMemory() {
        return memoryEnabled && (mode == ChatMode.CONVERSATION || mode == ChatMode.AGENT);
    }

    /**
     * 检查是否需要长期记忆存储
     * 只有站长会话才需要长期记忆存储
     * @return 是否需要长期记忆存储
     */
    public boolean needsPersistentMemory() {
        return isOwnerSession && needsMemory();
    }
    
    // ========== 信息查询方法 ==========

    /**
     * 获取消息数量
     * @return 消息数量
     */
    public int getMessageCount() {
        return memory.getMessageCount();
    }

    /**
     * 检查记忆是否为空
     * @return 记忆是否为空
     */
    public boolean isMemoryEmpty() {
        return memory.isEmpty();
    }

    /**
     * 获取记忆摘要
     * @return 记忆摘要
     */
    public String getMemorySummary() {
        return memory.getSummary();
    }

    /**
     * 获取会话的配置摘要
     * @return 配置摘要
     */
    public String getConfigurationSummary() {
        return String.format("用户:%s, 模式:%s, 轮次:%d, 消息:%d, 记忆:%s, 工具:%s, 世界书:%s",
                isOwnerSession ? "站长" : "游客",
                mode.getName(),
                conversationRounds,
                getMessageCount(),
                memoryEnabled ? "启用" : "禁用",
                toolsEnabled ? "启用" : "禁用",
                worldBookEnabled ? "启用" : "禁用");
    }

    /**
     * 获取会话状态摘要
     * @return 状态摘要
     */
    public String getStatusSummary() {
        return String.format("会话ID:%s, 角色:%s, 创建时间:%s, 最后活跃:%s, %s",
                sessionId.getValue(),
                characterId,
                createdAt.toString(),
                lastActiveAt.toString(),
                getConfigurationSummary());
    }
}
