package org.sounfury.aki.domain.character.advisor;

import org.springframework.ai.chat.client.advisor.api.Advisor;

/**
 * 角色Advisor领域服务接口
 * 负责创建角色相关的Advisor
 */
public interface CharacterAdvisorService {
    
    /**
     * 创建角色卡Advisor
     * 处理指定角色的完整角色卡信息注入
     * @param characterId 角色ID
     * @return 角色卡Advisor
     */
    Advisor createCharacterAdvisor(String characterId);
    
    /**
     * 创建默认角色Advisor
     * 处理默认角色的角色卡信息注入
     * @return 默认角色Advisor
     */
    Advisor createDefaultCharacterAdvisor();
}
