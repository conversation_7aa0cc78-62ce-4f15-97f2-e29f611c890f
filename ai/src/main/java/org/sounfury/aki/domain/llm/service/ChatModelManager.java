package org.sounfury.aki.domain.llm.service;

import org.sounfury.aki.domain.llm.ModelCapability;
import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.springframework.ai.chat.model.ChatModel;

import java.util.Set;

/**
 * 聊天模型领域服务接口
 * 管理ChatModel的生命周期和能力
 */
public interface ChatModelManager {
    
    /**
     * 获取当前活跃的ChatModel实例
     * @return 当前ChatModel实例
     */
    ChatModel getCurrentChatModel();
    
    /**
     * 根据配置ID获取ChatModel实例
     * @param configurationId 配置ID
     * @return ChatModel实例
     */
    ChatModel getChatModelByConfiguration(String configurationId);
    
    /**
     * 刷新当前ChatModel实例
     * 当配置发生变更时调用
     */
    void refreshChatModel();
    
    /**
     * 获取当前模型的能力集合
     * @return 模型能力集合
     */
    Set<ModelCapability> getCurrentModelCapabilities();
    
    /**
     * 检查当前模型是否支持特定能力
     * @param capability 模型能力
     * @return 是否支持该能力
     */
    boolean hasCapability(ModelCapability capability);
    
    /**
     * 获取当前LLM配置
     * @return 当前LLM配置
     */
    LlmConfiguration getCurrentConfiguration();
    
    /**
     * 检查当前配置是否有效
     * @return 配置是否有效
     */
    boolean isCurrentConfigurationValid();
}
