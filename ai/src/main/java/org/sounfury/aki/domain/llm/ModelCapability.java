package org.sounfury.aki.domain.llm;

/**
 * 模型能力枚举
 * 定义LLM模型支持的各种能力
 */
public enum ModelCapability {
    
    /**
     * 函数调用能力（Function Call / Tool Use）
     * 模型能够理解和调用外部工具函数
     */
    FUNCTION_CALL("function_call", "函数调用", "支持调用外部工具和函数"),
    
    /**
     * 流式输出能力
     * 模型支持实时流式返回响应
     */
    STREAMING("streaming", "流式输出", "支持实时流式返回响应内容"),
    
    /**
     * 视觉理解能力
     * 模型能够理解和分析图像内容
     */
    VISION("vision", "视觉理解", "支持图像理解和分析"),
    
    /**
     * 代码生成能力
     * 模型擅长生成和理解代码
     */
    CODE_GENERATION("code_generation", "代码生成", "擅长生成和理解各种编程语言代码"),
    
    /**
     * 多语言支持
     * 模型支持多种自然语言
     */
    MULTILINGUAL("multilingual", "多语言", "支持多种自然语言的理解和生成"),
    
    /**
     * 长文本处理
     * 模型支持处理长文本内容
     */
    LONG_CONTEXT("long_context", "长文本", "支持处理长文本内容，上下文窗口较大"),
    
    /**
     * 数学推理能力
     * 模型擅长数学计算和逻辑推理
     */
    MATHEMATICAL_REASONING("mathematical_reasoning", "数学推理", "擅长数学计算和逻辑推理"),
    
    /**
     * 文档分析能力
     * 模型能够分析和理解结构化文档
     */
    DOCUMENT_ANALYSIS("document_analysis", "文档分析", "支持分析和理解结构化文档"),
    
    /**
     * 角色扮演能力
     * 模型擅长角色扮演和对话
     */
    ROLE_PLAYING("role_playing", "角色扮演", "擅长角色扮演和情景对话"),
    
    /**
     * 创意写作能力
     * 模型擅长创意写作和内容创作
     */
    CREATIVE_WRITING("creative_writing", "创意写作", "擅长创意写作和内容创作"),
    
    /**
     * 数据分析能力
     * 模型能够分析和解释数据
     */
    DATA_ANALYSIS("data_analysis", "数据分析", "支持数据分析和统计解释"),
    
    /**
     * 实时信息获取
     * 模型能够获取实时信息（需要工具支持）
     */
    REAL_TIME_INFO("real_time_info", "实时信息", "支持获取实时信息和最新数据"),
    
    /**
     * 文件处理能力
     * 模型能够处理各种文件格式
     */
    FILE_PROCESSING("file_processing", "文件处理", "支持处理多种文件格式"),
    
    /**
     * 网络搜索能力
     * 模型能够进行网络搜索（需要工具支持）
     */
    WEB_SEARCH("web_search", "网络搜索", "支持网络搜索和信息检索");
    
    private final String code;
    private final String name;
    private final String description;
    
    ModelCapability(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取能力
     */
    public static ModelCapability fromCode(String code) {
        for (ModelCapability capability : values()) {
            if (capability.code.equals(code)) {
                return capability;
            }
        }
        throw new IllegalArgumentException("未知的模型能力代码: " + code);
    }
    
    /**
     * 检查是否为核心能力（影响编排模式的关键能力）
     */
    public boolean isCoreCapability() {
        return this == FUNCTION_CALL || this == STREAMING;
    }
    
    /**
     * 检查是否为Agent模式必需能力
     */
    public boolean isRequiredForAgent() {
        return this == FUNCTION_CALL;
    }
    
    /**
     * 检查是否为对话模式推荐能力
     */
    public boolean isRecommendedForConversation() {
        return this == ROLE_PLAYING || this == CREATIVE_WRITING || this == MULTILINGUAL;
    }
    
    /**
     * 检查是否为动作模式推荐能力
     */
    public boolean isRecommendedForAction() {
        return this == CODE_GENERATION || this == DOCUMENT_ANALYSIS || this == DATA_ANALYSIS;
    }
}
