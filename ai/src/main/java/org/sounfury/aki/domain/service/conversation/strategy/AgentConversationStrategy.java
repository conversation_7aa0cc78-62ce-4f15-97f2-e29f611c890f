package org.sounfury.aki.domain.service.conversation.strategy;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.character.advisor.CharacterAdvisorService;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.sounfury.aki.domain.prompt.advisor.PromptAdvisorFactory;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent对话策略实现
 * 处理Agent对话，使用Advisor模式处理提示词组装
 * 启用记忆、角色卡和工具调用
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgentConversationStrategy implements ConversationStrategy {

    private final AdvisorAwareChatClientManager chatClientManager;
    private final PromptAdvisorFactory promptAdvisorFactory;
    private final CharacterAdvisorService characterAdvisorService;
    
    @Override
    public ConversationResult execute(ConversationRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        log.info("开始执行Agent对话，会话ID: {}", request.session().getSessionId().getValue());

        try {
            // 1. 组装Advisor链
            List<DomainAdvisor> advisors = buildAdvisors(request);

            // 2. 获取Advisor驱动的ChatClient（自动包含工具）
            ChatClient chatClient = chatClientManager.createChatClient(advisors, request.session().getMode());

            // 3. 直接调用（Advisor处理所有横切关注点）
            ChatResponse chatResponse = chatClient
                    .prompt()
                    .user(request.userInput())
                    .call()
                    .chatResponse();

            String response = chatResponse.getResult().getOutput().getText();

            log.info("Agent对话执行完成，会话ID: {}, 耗时: {}ms",
                    request.session().getSessionId().getValue(),
                    java.time.Duration.between(startTime, LocalDateTime.now()).toMillis());

            return ConversationResult.success(response, getStrategyName(),
                    request.session().getSessionId().getValue());

        } catch (Exception e) {
            log.error("Agent对话执行失败，会话ID: {}",
                    request.session().getSessionId().getValue(), e);
            return ConversationResult.failure(e.getMessage(), getStrategyName(),
                    request.session().getSessionId().getValue());
        }
    }
    
    @Override
    public Flux<String> executeStream(ConversationRequest request) {
        log.info("开始执行流式Agent对话，会话ID: {}", request.session().getSessionId().getValue());

        try {
            // 1. 组装Advisor链
            List<DomainAdvisor> advisors = buildAdvisors(request);

            // 2. 获取Advisor驱动的ChatClient（自动包含工具）
            ChatClient chatClient = chatClientManager.createChatClient(advisors, request.session().getMode());

            // 3. 直接调用流式接口（Advisor处理所有横切关注点）
            return chatClient
                    .prompt()
                    .user(request.userInput())
                    .stream()
                    .content()
                    .doOnComplete(() -> {
                        log.info("流式Agent对话执行完成，会话ID: {}",
                                request.session().getSessionId().getValue());
                        // 注意：流式模式下需要在客户端收集完整响应后保存到记忆
                    })
                    .doOnError(error -> {
                        log.error("流式Agent对话执行失败，会话ID: {}",
                                request.session().getSessionId().getValue(), error);
                    });

        } catch (Exception e) {
            log.error("流式Agent对话执行异常，会话ID: {}",
                    request.session().getSessionId().getValue(), e);
            return Flux.error(e);
        }
    }
    
    @Override
    public String getStrategyName() {
        return "agent-conversation-strategy";
    }
    
    /**
     * 构建Advisor链
     * 定义Agent对话所需的所有Advisor及其执行顺序
     */
    private List<DomainAdvisor> buildAdvisors(ConversationRequest request) {
        return List.of(
                // 系统提示词Advisor（优先级100，最先执行）
                promptAdvisorFactory.createSystemPromptAdvisor(),

                // 角色卡Advisor（优先级200，处理角色人格、场景等）
                characterAdvisorService.createCharacterAdvisor(request.session().getCharacterId()),

                // 用户称呼Advisor（优先级300，处理用户名称）
                promptAdvisorFactory.createUserAddressAdvisor(request.userName()),

                // 行为指导Advisor（优先级400，处理Agent行为指导）
                promptAdvisorFactory.createBehaviorGuideAdvisor(TemplateType.AGENT_BEHAVIOR)

                // 注意：Agent模式的工具调用由AdvisorAwareChatClientManager根据ChatMode自动处理
                // 记忆处理将在未来的SessionMemoryAdvisor中实现
                // 权限控制将在未来的PermissionAdvisor中实现
        );
    }
    

    

}
