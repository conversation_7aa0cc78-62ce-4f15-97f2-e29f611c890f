package org.sounfury.aki.domain.chatsession;

import java.util.Optional;

/**
 * 会话领域服务接口
 * 封装会话相关的业务逻辑
 */
public interface SessionDomainManager {
    
    /**
     * 创建新的聊天会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @param isOwnerSession 是否是站长会话
     * @return 聊天会话
     */
    ChatSession createSession(SessionId sessionId, ChatMode mode, String characterId, boolean isOwnerSession);

    /**
     * 创建站长会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 站长聊天会话
     */
    default ChatSession createOwnerSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createSession(sessionId, mode, characterId, true);
    }

    /**
     * 创建游客会话
     * @param sessionId 会话ID
     * @param mode 聊天模式
     * @param characterId 角色ID
     * @return 游客聊天会话
     */
    default ChatSession createGuestSession(SessionId sessionId, ChatMode mode, String characterId) {
        return createSession(sessionId, mode, characterId, false);
    }
    
    /**
     * 获取聊天会话
     * @param sessionId 会话ID
     * @return 聊天会话，如果不存在则返回空
     */
    Optional<ChatSession> getSession(SessionId sessionId);
    
    /**
     * 更新会话的最后活跃时间
     * @param sessionId 会话ID
     * @return 是否更新成功
     */
    boolean updateLastActiveTime(SessionId sessionId);

    /**
     * 添加用户消息到会话
     * @param sessionId 会话ID
     * @param content 消息内容
     * @return 是否添加成功
     */
    boolean addUserMessage(SessionId sessionId, String content);

    /**
     * 添加助手消息到会话
     * @param sessionId 会话ID
     * @param content 消息内容
     * @return 是否添加成功
     */
    boolean addAssistantMessage(SessionId sessionId, String content);

    /**
     * 添加完整的对话轮次
     * @param sessionId 会话ID
     * @param userContent 用户消息内容
     * @param assistantContent 助手回复内容
     * @return 是否添加成功
     */
    boolean addConversationRound(SessionId sessionId, String userContent, String assistantContent);

    /**
     * 清空会话记忆
     * @param sessionId 会话ID
     * @return 是否清空成功
     */
    boolean clearSessionMemory(SessionId sessionId);
    
    /**
     * 检查会话是否存在
     * @param sessionId 会话ID
     * @return 是否存在
     */
    boolean sessionExists(SessionId sessionId);
    
    /**
     * 删除会话
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    boolean deleteSession(SessionId sessionId);
    
    /**
     * 获取会话统计信息
     * @return 会话统计信息
     */
    SessionStats getSessionStats();
    
    /**
     * 清理过期会话
     * @param timeoutMinutes 超时分钟数
     * @return 清理的会话数量
     */
    int cleanupExpiredSessions(int timeoutMinutes);
    
    /**
     * 会话统计信息
     */
    record SessionStats(
            long totalSessions,
            long activeSessions
    ) {}
}
