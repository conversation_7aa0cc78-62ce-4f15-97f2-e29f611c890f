package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.advisor.PromptAdvisorService;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.task.TaskMode;
import org.sounfury.aki.infrastructure.advisor.advisors.BehaviorGuideAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.SystemPromptAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.TaskSpecificAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.UserAddressAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.stereotype.Service;

/**
 * Spring AI提示词Advisor服务实现
 * 基础设施层实现，负责创建具体的Advisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiPromptAdvisorService implements PromptAdvisorService {
    
    private final SystemPromptManager systemPromptManager;
    
    @Override
    public Advisor createSystemPromptAdvisor() {
        log.debug("创建系统提示词Advisor");
        return new SystemPromptAdvisor(systemPromptManager);
    }
    
    @Override
    public Advisor createUserAddressAdvisor(String userName) {
        log.debug("创建用户称呼Advisor，用户名: {}", userName);
        return new UserAddressAdvisor(systemPromptManager, userName);
    }
    
    @Override
    public Advisor createBehaviorGuideAdvisor(TemplateType behaviorType) {
        log.debug("创建行为指导Advisor，类型: {}", behaviorType.getName());
        return new BehaviorGuideAdvisor(systemPromptManager, behaviorType);
    }
    
    @Override
    public Advisor createTaskSpecificAdvisor(TaskMode taskMode) {
        log.debug("创建任务特定Advisor，任务模式: {}", taskMode.getCode());
        return new TaskSpecificAdvisor(systemPromptManager, taskMode);
    }
}
