package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.llm.ModelCapability;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.domain.llm.service.ChatModelManager;
import org.sounfury.aki.domain.llm.service.ToolIntegrationManager;
import org.sounfury.aki.infrastructure.advisor.factory.AdvisorFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 默认聊天客户端服务实现
 * 基于ChatModelService和ToolIntegrationService提供ChatClient实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultChatClientManager implements ChatClientManager {

    private final ChatModelManager chatModelManager;
    private final ToolIntegrationManager toolIntegrationManager;
    private final AdvisorFactory advisorFactory;
    
    @Override
    public ChatClient getBasicChatClient() {
        try {
            ChatModel chatModel = chatModelManager.getCurrentChatModel();
            ChatClient chatClient = ChatClient.builder(chatModel).build();
            
            log.debug("创建基础ChatClient成功");
            return chatClient;
        } catch (Exception e) {
            log.error("创建基础ChatClient失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建基础ChatClient失败", e);
        }
    }
    
    @Override
    public ChatClient getChatClientWithTools(List<Object> tools) {
        try {
            ChatModel chatModel = chatModelManager.getCurrentChatModel();
            
            if (tools == null || tools.isEmpty()) {
                log.debug("工具列表为空，创建基础ChatClient");
                return ChatClient.builder(chatModel).build();
            }
            
            // 检查当前模型是否支持工具调用
            if (!supportsToolCalling()) {
                log.warn("当前模型不支持工具调用，创建基础ChatClient");
                return ChatClient.builder(chatModel).build();
            }
            
            ChatClient chatClient = ChatClient.builder(chatModel)
                    .defaultTools(tools.toArray())
                    .build();
            
            log.debug("创建带工具的ChatClient成功，工具数量: {}", tools.size());
            return chatClient;
        } catch (Exception e) {
            log.error("创建带工具的ChatClient失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建带工具的ChatClient失败", e);
        }
    }
    
    @Override
    public ChatClient getChatClientForMode(ChatMode mode, List<Object> tools) {
        if (mode == null) {
            log.debug("聊天模式为空，创建基础ChatClient");
            return getBasicChatClient();
        }
        
        try {
            // 根据模式决定是否使用工具
            if (toolIntegrationManager.supportsTools(mode) && tools != null && !tools.isEmpty()) {
                log.debug("为模式 {} 创建带工具的ChatClient", mode.getName());
                return getChatClientWithTools(tools);
            } else {
                log.debug("为模式 {} 创建基础ChatClient", mode.getName());
                return getBasicChatClient();
            }
        } catch (Exception e) {
            log.error("为模式 {} 创建ChatClient失败: {}", mode.getName(), e.getMessage(), e);
            // 降级处理：返回基础ChatClient
            return getBasicChatClient();
        }
    }
    
    @Override
    public boolean supportsToolCalling() {
        try {
            return chatModelManager.hasCapability(ModelCapability.FUNCTION_CALL);
        } catch (Exception e) {
            log.debug("检查工具调用支持失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean supportsStreaming() {
        try {
            return chatModelManager.hasCapability(ModelCapability.STREAMING);
        } catch (Exception e) {
            log.debug("检查流式输出支持失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public ChatClient getChatClientWithAdvisors(List<DomainAdvisor> advisors) {
        try {
            ChatModel chatModel = chatModelManager.getCurrentChatModel();

            if (advisors == null || advisors.isEmpty()) {
                log.debug("Advisor列表为空，创建基础ChatClient");
                return ChatClient.builder(chatModel).build();
            }

            // 将DomainAdvisor转换为Spring AI Advisor
            List<Advisor> springAiAdvisors = advisorFactory.createSpringAiAdvisors(advisors);

            ChatClient chatClient = ChatClient.builder(chatModel)
                    .defaultAdvisors(springAiAdvisors.toArray(new Advisor[0]))
                    .build();

            log.debug("创建带Advisor的ChatClient成功，Advisor数量: {}", advisors.size());
            return chatClient;
        } catch (Exception e) {
            log.error("创建带Advisor的ChatClient失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建带Advisor的ChatClient失败", e);
        }
    }

    @Override
    public ChatClient getChatClientWithAdvisorsAndTools(List<DomainAdvisor> advisors, List<Object> tools) {
        try {
            ChatModel chatModel = chatModelManager.getCurrentChatModel();

            // 构建ChatClient Builder
            ChatClient.Builder builder = ChatClient.builder(chatModel);

            // 添加Advisor
            if (advisors != null && !advisors.isEmpty()) {
                List<Advisor> springAiAdvisors = advisorFactory.createSpringAiAdvisors(advisors);
                builder.defaultAdvisors(springAiAdvisors.toArray(new Advisor[0]));
                log.debug("添加Advisor数量: {}", advisors.size());
            }

            // 添加工具
            if (tools != null && !tools.isEmpty() && supportsToolCalling()) {
                builder.defaultTools(tools.toArray());
                log.debug("添加工具数量: {}", tools.size());
            } else if (tools != null && !tools.isEmpty()) {
                log.warn("当前模型不支持工具调用，忽略工具配置");
            }

            ChatClient chatClient = builder.build();
            log.debug("创建带Advisor和工具的ChatClient成功");
            return chatClient;
        } catch (Exception e) {
            log.error("创建带Advisor和工具的ChatClient失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建带Advisor和工具的ChatClient失败", e);
        }
    }
}
