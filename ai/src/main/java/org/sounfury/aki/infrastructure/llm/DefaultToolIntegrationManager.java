package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.llm.service.ToolIntegrationManager;
import org.sounfury.aki.orchestration.component.impl.FunctionToolProvider;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 默认工具集成服务实现
 * 基于FunctionToolProvider管理工具集成
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultToolIntegrationManager implements ToolIntegrationManager {
    
    private final FunctionToolProvider toolProvider;
    
    @Override
    public List<Object> getAvailableTools() {
        try {
            return toolProvider.getAvailableTools();
        } catch (Exception e) {
            log.error("获取可用工具失败: {}", e.getMessage(), e);
            return List.of(); // 返回空列表作为降级处理
        }
    }
    
    @Override
    public boolean supportsTools(ChatMode mode) {
        if (mode == null) {
            return false;
        }
        
        // 只有Agent模式支持工具调用
        return mode == ChatMode.AGENT;
    }
    
    @Override
    public List<Object> getToolsForMode(ChatMode mode) {
        if (!supportsTools(mode)) {
            log.debug("聊天模式 {} 不支持工具调用", mode.getName());
            return List.of();
        }
        
        return getAvailableTools();
    }
    
    @Override
    public boolean areToolsAvailable() {
        try {
            List<Object> tools = getAvailableTools();
            return tools != null && !tools.isEmpty();
        } catch (Exception e) {
            log.debug("检查工具可用性失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public int getToolCount() {
        try {
            return getAvailableTools().size();
        } catch (Exception e) {
            log.debug("获取工具数量失败: {}", e.getMessage());
            return 0;
        }
    }
    
    @Override
    public List<String> getToolNames() {
        try {
            return toolProvider.getToolNames();
        } catch (Exception e) {
            log.error("获取工具名称列表失败: {}", e.getMessage(), e);
            return List.of();
        }
    }
}
