package org.sounfury.aki.infrastructure.advisor;

import org.sounfury.aki.domain.common.DomainAdvisor;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import reactor.core.publisher.Flux;

/**
 * Advisor执行器接口
 * 负责执行具体的Advisor逻辑
 */
public interface AdvisorExecutor {
    
    /**
     * 执行同步Advisor
     * @param domainAdvisor 领域Advisor
     * @param request 请求
     * @param chain 调用链
     * @return 响应
     */
    ChatClientResponse executeCall(DomainAdvisor domainAdvisor, ChatClientRequest request, CallAdvisorChain chain);
    
    /**
     * 执行流式Advisor
     * @param domainAdvisor 领域Advisor
     * @param request 请求
     * @param chain 调用链
     * @return 流式响应
     */
    Flux<ChatClientResponse> executeStream(DomainAdvisor domainAdvisor, ChatClientRequest request, StreamAdvisorChain chain);
}
