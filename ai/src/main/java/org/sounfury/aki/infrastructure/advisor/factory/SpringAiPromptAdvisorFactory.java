package org.sounfury.aki.infrastructure.advisor.factory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.prompt.advisor.PromptAdvisorFactory;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.service.task.enums.TaskMode;
import org.sounfury.aki.infrastructure.advisor.advisors.BehaviorGuideDomainAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.SystemPromptDomainAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.TaskSpecificDomainAdvisor;
import org.sounfury.aki.infrastructure.advisor.advisors.UserAddressDomainAdvisor;
import org.springframework.stereotype.Service;

/**
 * Spring AI提示词Advisor服务实现
 * 基础设施层实现，负责创建具体的DomainAdvisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiPromptAdvisorFactory implements PromptAdvisorFactory {

    private final SystemPromptManager systemPromptManager;

    @Override
    public DomainAdvisor createSystemPromptAdvisor() {
        log.debug("创建系统提示词Advisor");
        return new SystemPromptDomainAdvisor(systemPromptManager);
    }

    @Override
    public DomainAdvisor createUserAddressAdvisor(String userName) {
        log.debug("创建用户称呼Advisor，用户名: {}", userName);
        return new UserAddressDomainAdvisor(systemPromptManager, userName);
    }

    @Override
    public DomainAdvisor createBehaviorGuideAdvisor(TemplateType behaviorType) {
        log.debug("创建行为指导Advisor，类型: {}", behaviorType.getName());
        return new BehaviorGuideDomainAdvisor(systemPromptManager, behaviorType);
    }

    @Override
    public DomainAdvisor createTaskSpecificAdvisor(TaskMode taskMode) {
        log.debug("创建任务特定Advisor，任务模式: {}", taskMode.getCode());
        return new TaskSpecificDomainAdvisor(systemPromptManager, taskMode);
    }
}
