package org.sounfury.aki.infrastructure.advisor.factory;

import lombok.RequiredArgsConstructor;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.infrastructure.advisor.AdvisorExecutor;
import org.sounfury.aki.infrastructure.advisor.SpringAiAdvisorAdapter;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Spring ai的Advisor工厂
 * 负责将DomainAdvisor转换为Spring AI的Advisor
 */
@Component
@RequiredArgsConstructor
public class AdvisorFactory {
    
    private final AdvisorExecutor advisorExecutor;
    
    /**
     * 将DomainAdvisor转换为Spring AI Advisor
     * @param domainAdvisor 领域Advisor
     * @return Spring AI Advisor
     */
    public Advisor createSpringAiAdvisor(DomainAdvisor domainAdvisor) {
        return new SpringAiAdvisorAdapter(domainAdvisor, advisorExecutor);
    }
    
    /**
     * 批量转换DomainAdvisor为Spring AI Advisor
     * @param domainAdvisors 领域Advisor列表
     * @return Spring AI Advisor列表
     */
    public List<Advisor> createSpringAiAdvisors(List<DomainAdvisor> domainAdvisors) {
        return domainAdvisors.stream()
                .map(this::createSpringAiAdvisor)
                .toList();
    }
}
