package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.sounfury.aki.domain.task.TaskMode;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.ChatClientRequest;
import org.springframework.ai.chat.client.advisor.api.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务特定Advisor
 * 负责在请求中注入任务特定的提示词
 */
@Slf4j
@RequiredArgsConstructor
public class TaskSpecificAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final TaskMode taskMode;
    
    @Override
    public String getName() {
        return "TaskSpecificAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 500; // 在所有基础提示词之后
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("TaskSpecificAdvisor 开始处理请求，任务模式: {}", taskMode.getCode());
        
        // 1. 获取任务特定提示词
        String taskPrompt = systemPromptManager.buildTaskSpecificPrompt(taskMode.getCode());
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithTaskSpecific(request, taskPrompt);
        
        // 3. 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("TaskSpecificAdvisor 开始处理流式请求，任务模式: {}", taskMode.getCode());
        
        // 1. 获取任务特定提示词
        String taskPrompt = systemPromptManager.buildTaskSpecificPrompt(taskMode.getCode());
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithTaskSpecific(request, taskPrompt);
        
        // 3. 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 使用任务特定提示词增强请求
     */
    private ChatClientRequest enhanceWithTaskSpecific(ChatClientRequest request, String taskPrompt) {
        if (taskPrompt == null || taskPrompt.trim().isEmpty()) {
            log.debug("任务特定提示词为空，跳过增强");
            return request;
        }
        
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 在最后添加任务特定提示词
        messages.add(new SystemMessage(taskPrompt));
        log.debug("添加任务特定提示词: {}", taskMode.getCode());
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
