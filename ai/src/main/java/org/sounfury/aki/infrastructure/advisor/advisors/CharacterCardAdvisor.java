package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.ChatClientRequest;
import org.springframework.ai.chat.client.advisor.api.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 角色卡Advisor
 * 负责在请求中注入角色卡信息
 */
@Slf4j
@RequiredArgsConstructor
public class CharacterCardAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final CharacterPromptManager characterPromptManager;
    private final String characterId;
    
    @Override
    public String getName() {
        return "CharacterCardAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 200; // 在系统提示词之后，用户称呼之前
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("CharacterCardAdvisor 开始处理请求，角色ID: {}", characterId);
        
        // 1. 获取角色卡提示词
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(characterId);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithCharacter(request, characterPrompt);
        
        // 3. 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("CharacterCardAdvisor 开始处理流式请求，角色ID: {}", characterId);
        
        // 1. 获取角色卡提示词
        String characterPrompt = characterPromptManager.buildCompleteCharacterPrompt(characterId);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithCharacter(request, characterPrompt);
        
        // 3. 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 使用角色卡信息增强请求
     */
    private ChatClientRequest enhanceWithCharacter(ChatClientRequest request, String characterPrompt) {
        if (characterPrompt == null || characterPrompt.trim().isEmpty()) {
            log.debug("角色卡提示词为空，跳过增强");
            return request;
        }
        
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 在系统消息后添加角色卡提示词
        messages.add(new SystemMessage(characterPrompt));
        log.debug("添加角色卡提示词，角色ID: {}, 长度: {}", characterId, characterPrompt.length());
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
