package org.sounfury.aki.infrastructure.advisor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import reactor.core.publisher.Flux;

/**
 * Spring AI Advisor适配器
 * 将领域层的DomainAdvisor适配为Spring AI的Advisor接口
 */
@Slf4j
@RequiredArgsConstructor
public class SpringAiAdvisorAdapter implements CallAdvisor, StreamAdvisor {
    
    private final DomainAdvisor domainAdvisor;
    private final AdvisorExecutor advisorExecutor;
    
    @Override
    public String getName() {
        return domainAdvisor.getName();
    }
    
    @Override
    public int getOrder() {
        return domainAdvisor.getOrder();
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("SpringAiAdvisorAdapter 处理请求: {}", domainAdvisor.getName());
        
        // 委托给具体的执行器处理
        return advisorExecutor.executeCall(domainAdvisor, request, chain);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("SpringAiAdvisorAdapter 处理流式请求: {}", domainAdvisor.getName());
        
        // 委托给具体的执行器处理
        return advisorExecutor.executeStream(domainAdvisor, request, chain);
    }
}
