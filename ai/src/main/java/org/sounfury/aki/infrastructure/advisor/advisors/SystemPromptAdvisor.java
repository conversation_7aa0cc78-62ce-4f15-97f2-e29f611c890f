package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.springframework.ai.chat.client.advisor.api.AdvisorChain;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.ChatClientRequest;
import org.springframework.ai.chat.client.advisor.api.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统提示词Advisor
 * 负责在请求中注入基础系统提示词
 */
@Slf4j
@RequiredArgsConstructor
public class SystemPromptAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    
    @Override
    public String getName() {
        return "SystemPromptAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 100; // 最高优先级，最先执行
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("SystemPromptAdvisor 开始处理请求");
        
        // 1. 获取基础系统提示词
        String systemPrompt = systemPromptManager.buildBaseSystemPrompt();
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithSystemPrompt(request, systemPrompt);
        
        // 3. 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("SystemPromptAdvisor 开始处理流式请求");
        
        // 1. 获取基础系统提示词
        String systemPrompt = systemPromptManager.buildBaseSystemPrompt();
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithSystemPrompt(request, systemPrompt);
        
        // 3. 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 使用系统提示词增强请求
     */
    private ChatClientRequest enhanceWithSystemPrompt(ChatClientRequest request, String systemPrompt) {
        if (systemPrompt == null || systemPrompt.trim().isEmpty()) {
            log.debug("系统提示词为空，跳过增强");
            return request;
        }
        
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 在最前面添加系统消息（如果还没有系统消息）
        boolean hasSystemMessage = messages.stream()
                .anyMatch(msg -> msg instanceof SystemMessage);
        
        if (!hasSystemMessage) {
            messages.add(0, new SystemMessage(systemPrompt));
            log.debug("添加系统提示词: {}", systemPrompt.substring(0, Math.min(50, systemPrompt.length())));
        } else {
            log.debug("已存在系统消息，跳过添加");
        }
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
