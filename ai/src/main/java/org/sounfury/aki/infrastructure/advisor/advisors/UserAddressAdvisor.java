package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.ChatClientRequest;
import org.springframework.ai.chat.client.advisor.api.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户称呼Advisor
 * 负责在请求中注入用户称呼提示词
 */
@Slf4j
@RequiredArgsConstructor
public class UserAddressAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final String userName;
    
    @Override
    public String getName() {
        return "UserAddressAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 300; // 在系统提示词和角色卡之后
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("UserAddressAdvisor 开始处理请求，用户名: {}", userName);
        
        // 1. 获取用户称呼提示词
        String userAddressPrompt = systemPromptManager.buildUserAddressPrompt(userName);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithUserAddress(request, userAddressPrompt);
        
        // 3. 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("UserAddressAdvisor 开始处理流式请求，用户名: {}", userName);
        
        // 1. 获取用户称呼提示词
        String userAddressPrompt = systemPromptManager.buildUserAddressPrompt(userName);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithUserAddress(request, userAddressPrompt);
        
        // 3. 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 使用用户称呼提示词增强请求
     */
    private ChatClientRequest enhanceWithUserAddress(ChatClientRequest request, String userAddressPrompt) {
        if (userAddressPrompt == null || userAddressPrompt.trim().isEmpty()) {
            log.debug("用户称呼提示词为空，跳过增强");
            return request;
        }
        
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 在系统消息后添加用户称呼提示词
        messages.add(new SystemMessage(userAddressPrompt));
        log.debug("添加用户称呼提示词: {}", userAddressPrompt.substring(0, Math.min(50, userAddressPrompt.length())));
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
