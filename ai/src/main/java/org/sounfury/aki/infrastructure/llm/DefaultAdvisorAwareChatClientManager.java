package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.chatsession.ChatMode;
import org.sounfury.aki.domain.llm.service.AdvisorAwareChatClientManager;
import org.sounfury.aki.domain.llm.service.ChatClientManager;
import org.sounfury.aki.domain.llm.service.ToolIntegrationManager;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支持Advisor的ChatClient管理器实现
 * 为策略类提供便捷的Advisor驱动的ChatClient创建
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultAdvisorAwareChatClientManager implements AdvisorAwareChatClientManager {
    
    private final ChatClientManager chatClientManager;
    private final ToolIntegrationManager toolIntegrationManager;
    
    @Override
    public ChatClient createChatClient(List<DomainAdvisor> advisors) {
        log.debug("创建基于Advisor的ChatClient，Advisor数量: {}", 
                advisors != null ? advisors.size() : 0);
        
        return chatClientManager.getChatClientWithAdvisors(advisors);
    }
    
    @Override
    public ChatClient createChatClient(List<DomainAdvisor> advisors, ChatMode mode) {
        log.debug("创建基于Advisor和模式的ChatClient，模式: {}, Advisor数量: {}", 
                mode != null ? mode.getName() : "null", 
                advisors != null ? advisors.size() : 0);
        
        // 根据模式获取工具
        List<Object> tools = null;
        if (mode != null && toolIntegrationManager.supportsTools(mode)) {
            tools = toolIntegrationManager.getToolsForMode(mode);
            log.debug("为模式 {} 获取到工具数量: {}", mode.getName(), tools.size());
        }
        
        return chatClientManager.getChatClientWithAdvisorsAndTools(advisors, tools);
    }
    
    @Override
    public ChatClient createChatClient(List<DomainAdvisor> advisors, ChatMode mode, List<Object> tools) {
        log.debug("创建完全配置的ChatClient，模式: {}, Advisor数量: {}, 工具数量: {}", 
                mode != null ? mode.getName() : "null",
                advisors != null ? advisors.size() : 0,
                tools != null ? tools.size() : 0);
        
        return chatClientManager.getChatClientWithAdvisorsAndTools(advisors, tools);
    }
}
