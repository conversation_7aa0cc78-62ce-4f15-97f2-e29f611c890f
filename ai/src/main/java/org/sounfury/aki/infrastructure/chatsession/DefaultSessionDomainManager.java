package org.sounfury.aki.infrastructure.chatsession;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.chatsession.*;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 默认会话领域服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultSessionDomainManager implements SessionDomainManager {
    
    private final SessionRepository sessionRepository;
    
    @Override
    public ChatSession createSession(SessionId sessionId, ChatMode mode, String characterId, boolean isOwnerSession) {
        log.debug("创建会话: sessionId={}, mode={}, characterId={}, isOwner={}",
                sessionId.getValue(), mode, characterId, isOwnerSession);

        ChatSession session = ChatSession.createForMode(sessionId, mode, characterId, isOwnerSession);
        sessionRepository.save(session);

        log.info("会话创建成功: {}, 配置: {}",
                sessionId.getValue(), session.getConfigurationSummary());

        return session;
    }
    
    @Override
    public Optional<ChatSession> getSession(SessionId sessionId) {
        return sessionRepository.findById(sessionId);
    }
    
    @Override
    public boolean updateLastActiveTime(SessionId sessionId) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().updateLastActiveTime();
            sessionRepository.save(updatedSession);
            log.debug("更新会话活跃时间: {}", sessionId.getValue());
            return true;
        }
        
        log.debug("会话不存在，无法更新活跃时间: {}", sessionId.getValue());
        return false;
    }
    
    @Override
    public boolean sessionExists(SessionId sessionId) {
        return sessionRepository.exists(sessionId);
    }
    
    @Override
    public boolean deleteSession(SessionId sessionId) {
        if (sessionRepository.exists(sessionId)) {
            sessionRepository.deleteById(sessionId);
            log.info("删除会话: {}", sessionId.getValue());
            return true;
        }
        
        log.debug("会话不存在，无法删除: {}", sessionId.getValue());
        return false;
    }
    
    @Override
    public SessionStats getSessionStats() {
        long totalSessions = sessionRepository.count();
        // 简化实现，假设所有会话都是活跃的
        // 实际实现中可以根据最后活跃时间计算活跃会话数
        long activeSessions = totalSessions;
        
        return new SessionStats(totalSessions, activeSessions);
    }
    
    @Override
    public int cleanupExpiredSessions(int timeoutMinutes) {
        return sessionRepository.cleanupExpiredSessions(timeoutMinutes);
    }

    @Override
    public boolean addUserMessage(SessionId sessionId, String content) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().addUserMessage(content);
            sessionRepository.save(updatedSession);
            log.debug("添加用户消息到会话: {}", sessionId.getValue());
            return true;
        }

        log.debug("会话不存在，无法添加用户消息: {}", sessionId.getValue());
        return false;
    }

    @Override
    public boolean addAssistantMessage(SessionId sessionId, String content) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().addAssistantMessage(content);
            sessionRepository.save(updatedSession);
            log.debug("添加助手消息到会话: {}", sessionId.getValue());
            return true;
        }

        log.debug("会话不存在，无法添加助手消息: {}", sessionId.getValue());
        return false;
    }

    @Override
    public boolean addConversationRound(SessionId sessionId, String userContent, String assistantContent) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().addConversationRound(userContent, assistantContent);
            sessionRepository.save(updatedSession);
            log.debug("添加对话轮次到会话: {}", sessionId.getValue());
            return true;
        }

        log.debug("会话不存在，无法添加对话轮次: {}", sessionId.getValue());
        return false;
    }

    @Override
    public boolean clearSessionMemory(SessionId sessionId) {
        Optional<ChatSession> sessionOpt = sessionRepository.findById(sessionId);
        if (sessionOpt.isPresent()) {
            ChatSession updatedSession = sessionOpt.get().clearMemory();
            sessionRepository.save(updatedSession);
            log.info("清空会话记忆: {}", sessionId.getValue());
            return true;
        }

        log.debug("会话不存在，无法清空记忆: {}", sessionId.getValue());
        return false;
    }
}
