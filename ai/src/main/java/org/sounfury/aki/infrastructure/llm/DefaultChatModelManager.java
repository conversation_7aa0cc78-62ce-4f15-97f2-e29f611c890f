package org.sounfury.aki.infrastructure.llm;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.llm.ModelCapability;
import org.sounfury.aki.domain.llm.ModelCapabilityRegistry;
import org.sounfury.aki.domain.llm.config.LlmConfiguration;
import org.sounfury.aki.domain.llm.config.LlmConfigurationRepository;
import org.sounfury.aki.domain.llm.service.ChatModelManager;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 默认聊天模型服务实现
 * 基于ConfigurableChatModelFactory管理ChatModel实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultChatModelManager implements ChatModelManager {
    
    private final ConfigurableChatModelFactory chatModelFactory;
    private final LlmConfigurationRepository configurationRepository;
    
    @Override
    public ChatModel getCurrentChatModel() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            return chatModelFactory.createOrGetChatModel(configuration);
        } catch (Exception e) {
            log.error("获取当前ChatModel失败，使用默认配置: {}", e.getMessage(), e);
            LlmConfiguration defaultConfig = LlmConfiguration.createDefault();
            return chatModelFactory.createOrGetChatModel(defaultConfig);
        }
    }
    
    @Override
    public ChatModel getChatModelByConfiguration(String configurationId) {
        if (configurationId == null || configurationId.trim().isEmpty()) {
            return getCurrentChatModel();
        }
        
        try {
            LlmConfiguration configuration = configurationRepository.findById(configurationId)
                    .orElseThrow(() -> new RuntimeException("配置不存在: " + configurationId));
            
            return chatModelFactory.createOrGetChatModel(configuration);
        } catch (Exception e) {
            log.error("根据配置ID获取ChatModel失败: {}, 错误: {}", configurationId, e.getMessage());
            return getCurrentChatModel();
        }
    }
    
    @Override
    public void refreshChatModel() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            chatModelFactory.recreateChatModel(configuration);
            log.info("ChatModel刷新成功: Provider={}, Model={}", 
                    configuration.getProvider().getDisplayName(),
                    configuration.getProvider().getModelName());
        } catch (Exception e) {
            log.error("刷新ChatModel失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public Set<ModelCapability> getCurrentModelCapabilities() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            return ModelCapabilityRegistry.getModelCapabilities(
                    configuration.getProvider()
            );
        } catch (Exception e) {
            log.error("获取模型能力失败: {}", e.getMessage());
            return Set.of(); // 返回空集合作为降级处理
        }
    }
    
    @Override
    public boolean hasCapability(ModelCapability capability) {
        Set<ModelCapability> capabilities = getCurrentModelCapabilities();
        return capabilities.contains(capability);
    }
    
    @Override
    public LlmConfiguration getCurrentConfiguration() {
        return configurationRepository.findGlobalConfiguration()
                .orElseThrow(() -> new RuntimeException("全局LLM配置不存在"));
    }
    
    @Override
    public boolean isCurrentConfigurationValid() {
        try {
            LlmConfiguration configuration = getCurrentConfiguration();
            return configuration.isValid() && configuration.isEnabled();
        } catch (Exception e) {
            log.debug("检查配置有效性失败: {}", e.getMessage());
            return false;
        }
    }
}
