package org.sounfury.aki.infrastructure.advisor.factory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.common.DomainAdvisor;
import org.sounfury.aki.domain.character.advisor.CharacterAdvisorService;
import org.sounfury.aki.domain.prompt.template.service.CharacterPromptManager;
import org.sounfury.aki.infrastructure.advisor.advisors.CharacterCardDomainAdvisor;
import org.springframework.stereotype.Service;

/**
 * Spring AI角色Advisor服务实现
 * 基础设施层实现，负责创建具体的角色Advisor实例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SpringAiCharacterAdvisorFactory implements CharacterAdvisorService {
    
    private final CharacterPromptManager characterPromptManager;
    
    /**
     * 默认角色ID（酒保）
     */
    private static final String DEFAULT_CHARACTER_ID = "bartender";
    
    @Override
    public DomainAdvisor createCharacterAdvisor(String characterId) {
        log.debug("创建角色卡Advisor，角色ID: {}", characterId);
        return new CharacterCardDomainAdvisor(characterPromptManager, characterId);
    }

    @Override
    public DomainAdvisor createDefaultCharacterAdvisor() {
        log.debug("创建默认角色Advisor，角色ID: {}", DEFAULT_CHARACTER_ID);
        return new CharacterCardDomainAdvisor(characterPromptManager, DEFAULT_CHARACTER_ID);
    }
}
