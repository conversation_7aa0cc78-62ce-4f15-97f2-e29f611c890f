package org.sounfury.aki.infrastructure.advisor.advisors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sounfury.aki.domain.prompt.template.TemplateType;
import org.sounfury.aki.domain.prompt.template.service.SystemPromptManager;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.client.advisor.api.ChatClientRequest;
import org.springframework.ai.chat.client.advisor.api.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisor;
import org.springframework.ai.chat.client.advisor.api.StreamAdvisorChain;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;

/**
 * 行为指导Advisor
 * 负责在请求中注入行为指导提示词
 */
@Slf4j
@RequiredArgsConstructor
public class BehaviorGuideAdvisor implements CallAdvisor, StreamAdvisor {
    
    private final SystemPromptManager systemPromptManager;
    private final TemplateType behaviorType;
    
    @Override
    public String getName() {
        return "BehaviorGuideAdvisor";
    }
    
    @Override
    public int getOrder() {
        return 400; // 在其他基础提示词之后
    }
    
    @Override
    public ChatClientResponse adviseCall(ChatClientRequest request, CallAdvisorChain chain) {
        log.debug("BehaviorGuideAdvisor 开始处理请求，行为类型: {}", behaviorType.getName());
        
        // 1. 获取行为指导提示词
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(behaviorType);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithBehaviorGuide(request, behaviorPrompt);
        
        // 3. 继续链式调用
        return chain.nextCall(enhancedRequest);
    }
    
    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest request, StreamAdvisorChain chain) {
        log.debug("BehaviorGuideAdvisor 开始处理流式请求，行为类型: {}", behaviorType.getName());
        
        // 1. 获取行为指导提示词
        String behaviorPrompt = systemPromptManager.buildBehaviorGuidePrompt(behaviorType);
        
        // 2. 增强请求
        ChatClientRequest enhancedRequest = enhanceWithBehaviorGuide(request, behaviorPrompt);
        
        // 3. 继续链式调用
        return chain.nextStream(enhancedRequest);
    }
    
    /**
     * 使用行为指导提示词增强请求
     */
    private ChatClientRequest enhanceWithBehaviorGuide(ChatClientRequest request, String behaviorPrompt) {
        if (behaviorPrompt == null || behaviorPrompt.trim().isEmpty()) {
            log.debug("行为指导提示词为空，跳过增强");
            return request;
        }
        
        // 获取现有的Prompt
        Prompt currentPrompt = request.prompt();
        List<org.springframework.ai.chat.messages.Message> messages = new ArrayList<>(currentPrompt.getInstructions());
        
        // 在最后添加行为指导提示词
        messages.add(new SystemMessage(behaviorPrompt));
        log.debug("添加行为指导提示词: {}", behaviorType.getName());
        
        // 创建新的Prompt
        Prompt enhancedPrompt = new Prompt(messages, currentPrompt.getOptions());
        
        // 返回增强后的请求
        return request.mutate()
                .prompt(enhancedPrompt)
                .build();
    }
}
